//+------------------------------------------------------------------+
//|                                           Gold Scalping EA.mq5 |
//|                        Copyright 2024, Gold Scalping Strategy   |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Gold Scalping Strategy"
#property link      "https://www.mql5.com"
#property version   "1.00"

//--- Input parameters
input group "=== Strategy Parameters ==="
input int InpEMA_Fast = 50;                    // Fast EMA Period
input int InpEMA_Slow = 200;                   // Slow EMA Period
input int InpRSI_Period = 14;                  // RSI Period
input int InpATR_Period = 14;                  // ATR Period
input double InpRSI_Lower = 45.0;              // RSI Lower Bound
input double InpRSI_Upper = 55.0;              // RSI Upper Bound
input bool InpUseEngulfing = true;             // Use Engulfing Candle Pattern

input group "=== Risk Management ==="
input double InpLotSize = 0.01;                // Lot Size
input double InpTakeProfit = 20.0;             // Take Profit (Pips)
input double InpATR_Multiplier = 2.0;          // ATR Multiplier for Stop Loss
input int InpMagicNumber = 123456;             // Magic Number

input group "=== Trade Management ==="
input bool InpAllowBuy = true;                 // Allow Buy Orders
input bool InpAllowSell = true;                // Allow Sell Orders
input int InpMaxPositions = 1;                 // Maximum Positions

//--- Global variables
int handle_ema_fast, handle_ema_slow, handle_rsi, handle_atr;
double ema_fast[], ema_slow[], rsi[], atr[];
bool new_bar_flag = false;

//--- Trade management
#include <Trade\Trade.mqh>
CTrade trade;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Initialize indicators
    handle_ema_fast = iMA(_Symbol, PERIOD_CURRENT, InpEMA_Fast, 0, MODE_EMA, PRICE_CLOSE);
    handle_ema_slow = iMA(_Symbol, PERIOD_CURRENT, InpEMA_Slow, 0, MODE_EMA, PRICE_CLOSE);
    handle_rsi = iRSI(_Symbol, PERIOD_CURRENT, InpRSI_Period, PRICE_CLOSE);
    handle_atr = iATR(_Symbol, PERIOD_CURRENT, InpATR_Period);
    
    // Check if indicators are created successfully
    if(handle_ema_fast == INVALID_HANDLE || handle_ema_slow == INVALID_HANDLE || 
       handle_rsi == INVALID_HANDLE || handle_atr == INVALID_HANDLE)
    {
        Print("Error creating indicators");
        return INIT_FAILED;
    }
    
    // Set magic number for trade class
    trade.SetExpertMagicNumber(InpMagicNumber);
    
    // Set array as series
    ArraySetAsSeries(ema_fast, true);
    ArraySetAsSeries(ema_slow, true);
    ArraySetAsSeries(rsi, true);
    ArraySetAsSeries(atr, true);
    
    Print("Gold Scalping EA initialized successfully");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Release indicator handles
    IndicatorRelease(handle_ema_fast);
    IndicatorRelease(handle_ema_slow);
    IndicatorRelease(handle_rsi);
    IndicatorRelease(handle_atr);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check for new bar
    if(!IsNewBar()) return;

    Print("New bar detected - checking for signals");

    // Get current indicator values
    if(!GetIndicatorValues())
    {
        Print("Failed to get indicator values");
        return;
    }

    // Check current positions
    int current_positions = CountPositions();
    Print("Current positions: ", current_positions, " Max allowed: ", InpMaxPositions);
    if(current_positions >= InpMaxPositions)
    {
        Print("Maximum positions reached");
        return;
    }

    // Get current market data
    MqlRates rates[];
    if(CopyRates(_Symbol, PERIOD_CURRENT, 0, 3, rates) < 3)
    {
        Print("Failed to get market data");
        return;
    }
    ArraySetAsSeries(rates, true);

    Print("Checking signals - EMA Fast: ", ema_fast[0], " EMA Slow: ", ema_slow[0], " RSI: ", rsi[0]);

    // Check for trade signals
    CheckForBuySignal(rates);
    CheckForSellSignal(rates);
}

//+------------------------------------------------------------------+
//| Check if new bar formed                                          |
//+------------------------------------------------------------------+
bool IsNewBar()
{
    static datetime last_time = 0;
    datetime current_time = iTime(_Symbol, PERIOD_CURRENT, 0);
    
    if(current_time != last_time)
    {
        last_time = current_time;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Get indicator values                                             |
//+------------------------------------------------------------------+
bool GetIndicatorValues()
{
    // Copy indicator values
    if(CopyBuffer(handle_ema_fast, 0, 0, 3, ema_fast) < 3) return false;
    if(CopyBuffer(handle_ema_slow, 0, 0, 3, ema_slow) < 3) return false;
    if(CopyBuffer(handle_rsi, 0, 0, 3, rsi) < 3) return false;
    if(CopyBuffer(handle_atr, 0, 0, 3, atr) < 3) return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Check for buy signal                                             |
//+------------------------------------------------------------------+
void CheckForBuySignal(const MqlRates &rates[])
{
    if(!InpAllowBuy)
    {
        Print("Buy signals disabled");
        return;
    }

    // Check trend condition: 50 EMA > 200 EMA
    if(ema_fast[0] <= ema_slow[0])
    {
        Print("Buy rejected: EMA trend not bullish. Fast EMA: ", ema_fast[0], " Slow EMA: ", ema_slow[0]);
        return;
    }

    // Check RSI neutral zone (45-55)
    if(rsi[0] < InpRSI_Lower || rsi[0] > InpRSI_Upper)
    {
        Print("Buy rejected: RSI outside neutral zone. RSI: ", rsi[0], " Range: ", InpRSI_Lower, "-", InpRSI_Upper);
        return;
    }

    // Check for bullish engulfing pattern (if enabled)
    if(InpUseEngulfing && !IsBullishEngulfing(rates))
    {
        Print("Buy rejected: No bullish engulfing pattern");
        return;
    }

    // Check if price closed above 50 EMA
    if(rates[0].close <= ema_fast[0])
    {
        Print("Buy rejected: Price not above fast EMA. Close: ", rates[0].close, " Fast EMA: ", ema_fast[0]);
        return;
    }

    Print("All buy conditions met - executing buy order");
    // Execute buy order
    ExecuteBuyOrder();
}

//+------------------------------------------------------------------+
//| Check for sell signal                                            |
//+------------------------------------------------------------------+
void CheckForSellSignal(const MqlRates &rates[])
{
    if(!InpAllowSell)
    {
        Print("Sell signals disabled");
        return;
    }

    // Check trend condition: 50 EMA < 200 EMA
    if(ema_fast[0] >= ema_slow[0])
    {
        Print("Sell rejected: EMA trend not bearish. Fast EMA: ", ema_fast[0], " Slow EMA: ", ema_slow[0]);
        return;
    }

    // Check RSI neutral zone (45-55)
    if(rsi[0] < InpRSI_Lower || rsi[0] > InpRSI_Upper)
    {
        Print("Sell rejected: RSI outside neutral zone. RSI: ", rsi[0], " Range: ", InpRSI_Lower, "-", InpRSI_Upper);
        return;
    }

    // Check for bearish engulfing pattern (if enabled)
    if(InpUseEngulfing && !IsBearishEngulfing(rates))
    {
        Print("Sell rejected: No bearish engulfing pattern");
        return;
    }

    // Check if price closed below 50 EMA
    if(rates[0].close >= ema_fast[0])
    {
        Print("Sell rejected: Price not below fast EMA. Close: ", rates[0].close, " Fast EMA: ", ema_fast[0]);
        return;
    }

    Print("All sell conditions met - executing sell order");
    // Execute sell order
    ExecuteSellOrder();
}

//+------------------------------------------------------------------+
//| Check for bullish engulfing pattern                             |
//+------------------------------------------------------------------+
bool IsBullishEngulfing(const MqlRates &rates[])
{
    // Current candle should be bullish
    if(rates[0].close <= rates[0].open) return false;
    
    // Previous candle should be bearish
    if(rates[1].close >= rates[1].open) return false;
    
    // Current candle should engulf previous candle
    if(rates[0].open >= rates[1].close && rates[0].close >= rates[1].open)
        return true;
    
    return false;
}

//+------------------------------------------------------------------+
//| Check for bearish engulfing pattern                             |
//+------------------------------------------------------------------+
bool IsBearishEngulfing(const MqlRates &rates[])
{
    // Current candle should be bearish
    if(rates[0].close >= rates[0].open) return false;
    
    // Previous candle should be bullish
    if(rates[1].close <= rates[1].open) return false;
    
    // Current candle should engulf previous candle
    if(rates[0].open <= rates[1].close && rates[0].close <= rates[1].open)
        return true;
    
    return false;
}

//+------------------------------------------------------------------+
//| Execute buy order                                                |
//+------------------------------------------------------------------+
void ExecuteBuyOrder()
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
    
    // Calculate stop loss based on ATR
    double stop_loss = ask - (atr[0] * InpATR_Multiplier);
    
    // Calculate take profit (20 pips for gold)
    double take_profit = ask + (InpTakeProfit * point * 10); // 10 points = 1 pip for gold
    
    // Normalize prices
    stop_loss = NormalizeDouble(stop_loss, digits);
    take_profit = NormalizeDouble(take_profit, digits);
    
    // Execute buy order
    if(trade.Buy(InpLotSize, _Symbol, ask, stop_loss, take_profit, "Gold Scalping Buy"))
    {
        Print("Buy order executed at ", ask, " SL: ", stop_loss, " TP: ", take_profit);
    }
    else
    {
        Print("Failed to execute buy order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Execute sell order                                               |
//+------------------------------------------------------------------+
void ExecuteSellOrder()
{
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
    
    // Calculate stop loss based on ATR
    double stop_loss = bid + (atr[0] * InpATR_Multiplier);
    
    // Calculate take profit (20 pips for gold)
    double take_profit = bid - (InpTakeProfit * point * 10); // 10 points = 1 pip for gold
    
    // Normalize prices
    stop_loss = NormalizeDouble(stop_loss, digits);
    take_profit = NormalizeDouble(take_profit, digits);
    
    // Execute sell order
    if(trade.Sell(InpLotSize, _Symbol, bid, stop_loss, take_profit, "Gold Scalping Sell"))
    {
        Print("Sell order executed at ", bid, " SL: ", stop_loss, " TP: ", take_profit);
    }
    else
    {
        Print("Failed to execute sell order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Count current positions                                          |
//+------------------------------------------------------------------+
int CountPositions()
{
    int count = 0;
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionSelectByTicket(PositionGetTicket(i)))
        {
            if(PositionGetString(POSITION_SYMBOL) == _Symbol && 
               PositionGetInteger(POSITION_MAGIC) == InpMagicNumber)
            {
                count++;
            }
        }
    }
    return count;
}

//+------------------------------------------------------------------+
//| Trade transaction function                                       |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                       const MqlTradeRequest& request,
                       const MqlTradeResult& result)
{
    // Handle trade events if needed
    if(trans.symbol == _Symbol)
    {
        if(trans.type == TRADE_TRANSACTION_DEAL_ADD)
        {
            // Get deal info to check magic number
            if(HistoryDealSelect(trans.deal))
            {
                long deal_magic = HistoryDealGetInteger(trans.deal, DEAL_MAGIC);
                if(deal_magic == InpMagicNumber)
                {
                    Print("Trade executed: ", trans.symbol, " Volume: ", trans.volume, " Price: ", trans.price);
                }
            }
        }
    }
}